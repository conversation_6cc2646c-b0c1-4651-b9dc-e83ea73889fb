<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>
    <%= title %>
  </title>

  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="/favicon.ico">

  <!-- AdminLTE CSS -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/css/adminlte.min.css">

  <!-- Bootstrap 5 CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <!-- Bootstrap Icons -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">

  <!-- Chart.js -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.min.js" defer></script>

  <!-- Custom CSS -->
  <link rel="stylesheet" href="/css/admin-custom.css">

  <style>
    /* 顶栏优化样式 */
    .main-header.navbar {
      height: 60px;
      padding: 0 1rem;
      position: fixed !important;
      top: 0;
      z-index: 1030;
      width: 100%;
    }

    .navbar-nav {
      gap: 0.25rem;
    }

    .navbar-nav .nav-link {
      display: flex;
      align-items: center;
      padding: 0.5rem 0.75rem;
      transition: all 0.3s ease;
      border-radius: 6px;
      white-space: nowrap;
    }

    .navbar-nav .nav-link:hover {
      background-color: rgba(255, 255, 255, 0.15);
      transform: translateY(-1px);
    }

    /* 确保内容区不被导航栏遮挡 */
    .content-wrapper {
      margin-top: 60px;
    }

    .main-sidebar {
      padding-top: 60px;
    }

    .navbar-search-wrapper .form-control-navbar {
      background-color: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: white;
    }

    .navbar-search-wrapper .form-control-navbar::placeholder {
      color: rgba(255, 255, 255, 0.7);
    }

    .navbar-search-wrapper .btn-navbar {
      background-color: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: white;
    }

    .navbar-search-wrapper .btn-navbar:hover {
      background-color: rgba(255, 255, 255, 0.2);
    }

    .dropdown-menu {
      border-radius: 8px;
      border: none;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    }

    .dropdown-item {
      padding: 0.75rem 1rem;
      transition: all 0.2s ease;
    }

    .dropdown-item:hover {
      background-color: #f8f9fa;
      transform: translateX(2px);
    }

    .dropdown-header {
      background-color: #f8f9fa;
      border-radius: 8px 8px 0 0;
      margin: 0;
    }

    .dropdown-footer {
      background-color: #f8f9fa;
      border-radius: 0 0 8px 8px;
      margin: 0;
    }

    /* 侧边栏优化样式 */
    .nav-sidebar .nav-item {
      margin-bottom: 4px;
    }

    .nav-sidebar .nav-link {
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
    }

    .nav-sidebar .nav-link:hover {
      background-color: rgba(255, 255, 255, 0.1) !important;
      color: white !important;
      transform: translateX(3px);
    }

    .nav-sidebar .nav-link.active {
      background-color: rgba(255, 255, 255, 0.2) !important;
      color: white !important;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    /* 树形菜单动画 */
    .nav-treeview {
      transition: all 0.3s ease;
    }

    .nav-item:hover .fas.fa-angle-left {
      transform: rotate(-90deg);
    }

    .nav-item.menu-open .fas.fa-angle-left {
      transform: rotate(-90deg);
    }

    /* 子菜单项悬停效果 */
    .nav-treeview .nav-link:hover {
      background-color: rgba(255, 255, 255, 0.05) !important;
      transform: translateX(8px);
    }

    /* 响应式优化 */
    @media (max-width: 768px) {
      .navbar-text {
        display: none !important;
      }

      .navbar-search-wrapper {
        display: none !important;
      }

      .nav-sidebar {
        padding: 4px;
      }

      .nav-sidebar .nav-link {
        padding: 8px 10px !important;
        font-size: 14px;
      }

      .nav-treeview .nav-link {
        padding: 6px 14px !important;
        font-size: 13px;
      }
    }

    /* 通知徽章动画 */
    .badge.rounded-pill {
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0% {
        transform: scale(1);
      }

      50% {
        transform: scale(1.1);
      }

      100% {
        transform: scale(1);
      }
    }

    /* 用户头像优化 */
    .rounded-circle {
      transition: all 0.3s ease;
    }

    .nav-link:hover .rounded-circle {
      transform: scale(1.05);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }
  </style>
</head>

<body class="hold-transition sidebar-mini layout-fixed">
  <div class="wrapper">

    <!-- Navbar -->
    <nav class="main-header navbar navbar-expand navbar-primary navbar-dark"
      style="background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
      <!-- Left navbar links -->
      <ul class="navbar-nav">
        <li class="nav-item">
          <a class="nav-link" data-widget="pushmenu" href="#" role="button" title="切换侧边栏">
            <i class="fas fa-bars"></i>
          </a>
        </li>
        <li class="nav-item d-none d-sm-inline-block">
          <a href="/dashboard" class="nav-link">
            <i class="fas fa-home me-1"></i>首页
          </a>
        </li>
        <li class="nav-item d-none d-md-inline-block">
          <a href="/tenants" class="nav-link">
            <i class="fas fa-building me-1"></i>租户管理
          </a>
        </li>
      </ul>

      <!-- Center - Platform Title -->
      <div class="navbar-nav mx-auto d-none d-lg-flex">
        <span class="navbar-text text-white fw-bold d-flex align-items-center">
          <i class="fas fa-egg-fried me-2"></i>智慧养鹅 SaaS 管理平台
        </span>
      </div>

      <!-- Right navbar links -->
      <ul class="navbar-nav ms-auto align-items-center">
        <!-- Search -->
        <li class="nav-item d-none d-md-block me-2">
          <div class="navbar-search-wrapper">
            <form class="form-inline" onsubmit="return false;">
              <div class="input-group input-group-sm">
                <input class="form-control form-control-navbar" type="search" placeholder="搜索..." aria-label="Search"
                  style="width: 180px;">
                <div class="input-group-append">
                  <button class="btn btn-navbar" type="button">
                    <i class="fas fa-search"></i>
                  </button>
                </div>
              </div>
            </form>
          </div>
        </li>

        <!-- Notifications Dropdown Menu -->
        <li class="nav-item dropdown">
          <a class="nav-link position-relative" data-bs-toggle="dropdown" href="#" role="button" title="通知">
            <i class="far fa-bell fs-5"></i>
            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger"
              style="font-size: 0.6rem;">
              3
            </span>
          </a>
          <div class="dropdown-menu dropdown-menu-lg dropdown-menu-end shadow-lg border-0" style="min-width: 320px;">
            <div class="dropdown-header bg-light py-2">
              <strong>3 个新通知</strong>
            </div>
            <div class="dropdown-divider m-0"></div>
            <a href="#" class="dropdown-item py-3">
              <div class="d-flex align-items-center">
                <div class="flex-shrink-0">
                  <i class="fas fa-envelope text-primary"></i>
                </div>
                <div class="flex-grow-1 ms-3">
                  <div class="fw-bold">新用户注册</div>
                  <small class="text-muted">3 分钟前</small>
                </div>
              </div>
            </a>
            <div class="dropdown-divider m-0"></div>
            <a href="#" class="dropdown-item py-3">
              <div class="d-flex align-items-center">
                <div class="flex-shrink-0">
                  <i class="fas fa-chart-line text-success"></i>
                </div>
                <div class="flex-grow-1 ms-3">
                  <div class="fw-bold">系统性能报告</div>
                  <small class="text-muted">12 小时前</small>
                </div>
              </div>
            </a>
            <div class="dropdown-divider m-0"></div>
            <div class="dropdown-footer text-center py-2">
              <a href="#" class="text-decoration-none">查看所有通知</a>
            </div>
          </div>
        </li>

        <!-- User Menu -->
        <li class="nav-item dropdown">
          <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="navbarDropdown" role="button"
            data-bs-toggle="dropdown" aria-expanded="false">
            <img src="<%= user && user.avatar ? user.avatar : '/img/default-avatar.png' %>" class="rounded-circle me-2"
              style="width: 32px; height: 32px; object-fit: cover; border: 2px solid rgba(255,255,255,0.3);" alt="用户头像">
            <span class="d-none d-lg-inline-block text-white">
              <%= user ? user.name || user.username : '管理员' %>
            </span>
          </a>
          <ul class="dropdown-menu dropdown-menu-end shadow-lg border-0" style="min-width: 200px;">
            <li class="dropdown-header">
              <div class="text-center">
                <img src="<%= user && user.avatar ? user.avatar : '/img/default-avatar.png' %>"
                  class="rounded-circle mb-2" style="width: 48px; height: 48px; object-fit: cover;" alt="用户头像">
                <div class="fw-bold">
                  <%= user ? user.name || user.username : '管理员' %>
                </div>
                <small class="text-muted">系统管理员</small>
              </div>
            </li>
            <li>
              <hr class="dropdown-divider">
            </li>
            <li><a class="dropdown-item" href="/users/profile">
                <i class="fas fa-user me-2 text-primary"></i> 个人资料
              </a></li>
            <li><a class="dropdown-item" href="/system/settings">
                <i class="fas fa-cog me-2 text-secondary"></i> 系统设置
              </a></li>
            <li><a class="dropdown-item" href="/system/logs">
                <i class="fas fa-list-alt me-2 text-info"></i> 系统日志
              </a></li>
            <li>
              <hr class="dropdown-divider">
            </li>
            <li><a class="dropdown-item text-danger" href="#" id="logoutBtn">
                <i class="fas fa-sign-out-alt me-2"></i> 退出登录
              </a></li>
          </ul>
        </li>
      </ul>
    </nav>

    <!-- Main Sidebar Container -->
    <aside class="main-sidebar sidebar-dark-info elevation-4"
      style="background: linear-gradient(180deg, #1e3c72 0%, #2a5298 50%, #1a365d 100%);">
      <!-- Brand Logo -->
      <a href="/dashboard" class="brand-link"
        style="background: rgba(255,255,255,0.1); border-bottom: 1px solid rgba(255,255,255,0.1);">
        <img src="/img/logo.png" alt="智慧养鹅" class="brand-image img-circle elevation-3" style="opacity: .8"
          id="sidebarLogo">
        <span class="brand-text font-weight-light">智慧养鹅SAAS</span>
      </a>

      <!-- Sidebar -->
      <div class="sidebar">
        <!-- Sidebar Menu -->
        <nav class="mt-2">
          <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false" style="padding: 8px;">

            <!-- Dashboard -->
            <li class="nav-item mb-1">
              <a href="/dashboard" class="nav-link" style="padding: 10px 12px; border-radius: 8px; margin-bottom: 4px;">
                <i class="nav-icon fas fa-tachometer-alt" style="width: 20px; text-align: center;"></i>
                <p style="margin: 0; margin-left: 8px;">平台仪表盘</p>
              </a>
            </li>

            <!-- Tenant Management -->
            <li class="nav-item mb-1">
              <a href="#" class="nav-link" style="padding: 10px 12px; border-radius: 8px; margin-bottom: 4px;">
                <i class="nav-icon fas fa-building" style="width: 20px; text-align: center;"></i>
                <p style="margin: 0; margin-left: 8px; display: flex; align-items: center; justify-content: space-between;">
                  租户管理
                  <i class="fas fa-angle-left" style="transition: transform 0.3s ease;"></i>
                </p>
              </a>
              <ul class="nav nav-treeview" style="padding-left: 12px; margin-top: 4px;">
                <li class="nav-item">
                  <a href="/tenants" class="nav-link" style="padding: 8px 16px; border-radius: 6px; margin-bottom: 2px;">
                    <i class="far fa-circle nav-icon" style="width: 16px; text-align: center; font-size: 8px;"></i>
                    <p style="margin: 0; margin-left: 12px; font-size: 14px;">租户列表</p>
                  </a>
                </li>
                <li class="nav-item">
                  <a href="/tenants/subscriptions" class="nav-link" style="padding: 8px 16px; border-radius: 6px; margin-bottom: 2px;">
                    <i class="far fa-circle nav-icon" style="width: 16px; text-align: center; font-size: 8px;"></i>
                    <p style="margin: 0; margin-left: 12px; font-size: 14px;">订阅管理</p>
                  </a>
                </li>
                <li class="nav-item">
                  <a href="/tenants/usage" class="nav-link" style="padding: 8px 16px; border-radius: 6px; margin-bottom: 2px;">
                    <i class="far fa-circle nav-icon" style="width: 16px; text-align: center; font-size: 8px;"></i>
                    <p style="margin: 0; margin-left: 12px; font-size: 14px;">使用统计</p>
                  </a>
                </li>
              </ul>
            </li>

            <!-- Today Goose Price -->
            <li class="nav-item mb-1">
              <a href="#" class="nav-link" style="padding: 10px 12px; border-radius: 8px; margin-bottom: 4px;">
                <i class="nav-icon fas fa-coins" style="width: 20px; text-align: center;"></i>
                <p style="margin: 0; margin-left: 8px; display: flex; align-items: center; justify-content: space-between;">
                  今日鹅价
                  <i class="fas fa-angle-left" style="transition: transform 0.3s ease;"></i>
                </p>
              </a>
              <ul class="nav nav-treeview" style="padding-left: 12px; margin-top: 4px;">
                <li class="nav-item">
                  <a href="/goose-prices" class="nav-link" style="padding: 8px 16px; border-radius: 6px; margin-bottom: 2px;">
                    <i class="far fa-circle nav-icon" style="width: 16px; text-align: center; font-size: 8px;"></i>
                    <p style="margin: 0; margin-left: 12px; font-size: 14px;">价格管理</p>
                  </a>
                </li>
                <li class="nav-item">
                  <a href="/goose-prices/regions" class="nav-link" style="padding: 8px 16px; border-radius: 6px; margin-bottom: 2px;">
                    <i class="far fa-circle nav-icon" style="width: 16px; text-align: center; font-size: 8px;"></i>
                    <p style="margin: 0; margin-left: 12px; font-size: 14px;">地区配置</p>
                  </a>
                </li>
                <li class="nav-item">
                  <a href="/goose-prices/trends" class="nav-link" style="padding: 8px 16px; border-radius: 6px; margin-bottom: 2px;">
                    <i class="far fa-circle nav-icon" style="width: 16px; text-align: center; font-size: 8px;"></i>
                    <p style="margin: 0; margin-left: 12px; font-size: 14px;">价格趋势</p>
                  </a>
                </li>
              </ul>
            </li>

            <!-- Knowledge Base -->
            <li class="nav-item mb-1">
              <a href="#" class="nav-link" style="padding: 10px 12px; border-radius: 8px; margin-bottom: 4px;">
                <i class="nav-icon fas fa-book" style="width: 20px; text-align: center;"></i>
                <p style="margin: 0; margin-left: 8px; display: flex; align-items: center; justify-content: space-between;">
                  知识库管理
                  <i class="fas fa-angle-left" style="transition: transform 0.3s ease;"></i>
                </p>
              </a>
              <ul class="nav nav-treeview" style="padding-left: 12px; margin-top: 4px;">
                <li class="nav-item">
                  <a href="/knowledge" class="nav-link" style="padding: 8px 16px; border-radius: 6px; margin-bottom: 2px;">
                    <i class="far fa-circle nav-icon" style="width: 16px; text-align: center; font-size: 8px;"></i>
                    <p style="margin: 0; margin-left: 12px; font-size: 14px;">知识文章</p>
                  </a>
                </li>
                <li class="nav-item">
                  <a href="/knowledge/categories" class="nav-link" style="padding: 8px 16px; border-radius: 6px; margin-bottom: 2px;">
                    <i class="far fa-circle nav-icon" style="width: 16px; text-align: center; font-size: 8px;"></i>
                    <p style="margin: 0; margin-left: 12px; font-size: 14px;">分类管理</p>
                  </a>
                </li>
                <li class="nav-item">
                  <a href="/knowledge/tags" class="nav-link" style="padding: 8px 16px; border-radius: 6px; margin-bottom: 2px;">
                    <i class="far fa-circle nav-icon" style="width: 16px; text-align: center; font-size: 8px;"></i>
                    <p style="margin: 0; margin-left: 12px; font-size: 14px;">标签管理</p>
                  </a>
                </li>
              </ul>
            </li>

            <!-- Announcements -->
            <li class="nav-item mb-1">
              <a href="#" class="nav-link" style="padding: 10px 12px; border-radius: 8px; margin-bottom: 4px;">
                <i class="nav-icon fas fa-bullhorn" style="width: 20px; text-align: center;"></i>
                <p style="margin: 0; margin-left: 8px; display: flex; align-items: center; justify-content: space-between;">
                  公告管理
                  <i class="fas fa-angle-left" style="transition: transform 0.3s ease;"></i>
                </p>
              </a>
              <ul class="nav nav-treeview" style="padding-left: 12px; margin-top: 4px;">
                <li class="nav-item">
                  <a href="/announcements" class="nav-link" style="padding: 8px 16px; border-radius: 6px; margin-bottom: 2px;">
                    <i class="far fa-circle nav-icon" style="width: 16px; text-align: center; font-size: 8px;"></i>
                    <p style="margin: 0; margin-left: 12px; font-size: 14px;">公告列表</p>
                  </a>
                </li>
                <li class="nav-item">
                  <a href="/announcements/create" class="nav-link" style="padding: 8px 16px; border-radius: 6px; margin-bottom: 2px;">
                    <i class="far fa-circle nav-icon" style="width: 16px; text-align: center; font-size: 8px;"></i>
                    <p style="margin: 0; margin-left: 12px; font-size: 14px;">发布公告</p>
                  </a>
                </li>
              </ul>
            </li>

            <!-- Mall Management -->
            <li class="nav-item mb-1">
              <a href="#" class="nav-link" style="padding: 10px 12px; border-radius: 8px; margin-bottom: 4px;">
                <i class="nav-icon fas fa-shopping-cart" style="width: 20px; text-align: center;"></i>
                <p style="margin: 0; margin-left: 8px; display: flex; align-items: center; justify-content: space-between;">
                  商城管理
                  <i class="fas fa-angle-left" style="transition: transform 0.3s ease;"></i>
                </p>
              </a>
              <ul class="nav nav-treeview" style="padding-left: 12px; margin-top: 4px;">
                <li class="nav-item">
                  <a href="/mall/products" class="nav-link" style="padding: 8px 16px; border-radius: 6px; margin-bottom: 2px;">
                    <i class="far fa-circle nav-icon" style="width: 16px; text-align: center; font-size: 8px;"></i>
                    <p style="margin: 0; margin-left: 12px; font-size: 14px;">商品管理</p>
                  </a>
                </li>
                <li class="nav-item">
                  <a href="/mall/categories" class="nav-link" style="padding: 8px 16px; border-radius: 6px; margin-bottom: 2px;">
                    <i class="far fa-circle nav-icon" style="width: 16px; text-align: center; font-size: 8px;"></i>
                    <p style="margin: 0; margin-left: 12px; font-size: 14px;">分类管理</p>
                  </a>
                </li>
                <li class="nav-item">
                  <a href="/mall/orders" class="nav-link" style="padding: 8px 16px; border-radius: 6px; margin-bottom: 2px;">
                    <i class="far fa-circle nav-icon" style="width: 16px; text-align: center; font-size: 8px;"></i>
                    <p style="margin: 0; margin-left: 12px; font-size: 14px;">订单管理</p>
                  </a>
                </li>
                <li class="nav-item">
                  <a href="/mall/inventory" class="nav-link" style="padding: 8px 16px; border-radius: 6px; margin-bottom: 2px;">
                    <i class="far fa-circle nav-icon" style="width: 16px; text-align: center; font-size: 8px;"></i>
                    <p style="margin: 0; margin-left: 12px; font-size: 14px;">库存管理</p>
                  </a>
                </li>
              </ul>
            </li>

            <!-- API Management -->
            <li class="nav-item mb-1">
              <a href="#" class="nav-link" style="padding: 10px 12px; border-radius: 8px; margin-bottom: 4px;">
                <i class="nav-icon fas fa-plug" style="width: 20px; text-align: center;"></i>
                <p style="margin: 0; margin-left: 8px; display: flex; align-items: center; justify-content: space-between;">
                  API管理
                  <i class="fas fa-angle-left" style="transition: transform 0.3s ease;"></i>
                </p>
              </a>
              <ul class="nav nav-treeview" style="padding-left: 12px; margin-top: 4px;">
                <li class="nav-item">
                  <a href="/api/endpoints" class="nav-link" style="padding: 8px 16px; border-radius: 6px; margin-bottom: 2px;">
                    <i class="far fa-circle nav-icon" style="width: 16px; text-align: center; font-size: 8px;"></i>
                    <p style="margin: 0; margin-left: 12px; font-size: 14px;">接口管理</p>
                  </a>
                </li>
                <li class="nav-item">
                  <a href="/api/statistics" class="nav-link" style="padding: 8px 16px; border-radius: 6px; margin-bottom: 2px;">
                    <i class="far fa-circle nav-icon" style="width: 16px; text-align: center; font-size: 8px;"></i>
                    <p style="margin: 0; margin-left: 12px; font-size: 14px;">调用统计</p>
                  </a>
                </li>
                <li class="nav-item">
                  <a href="/api/monitor" class="nav-link" style="padding: 8px 16px; border-radius: 6px; margin-bottom: 2px;">
                    <i class="far fa-circle nav-icon" style="width: 16px; text-align: center; font-size: 8px;"></i>
                    <p style="margin: 0; margin-left: 12px; font-size: 14px;">性能监控</p>
                  </a>
                </li>
              </ul>
            </li>

            <!-- Platform Users -->
            <li class="nav-item mb-1">
              <a href="/platform-users" class="nav-link" style="padding: 10px 12px; border-radius: 8px; margin-bottom: 4px;">
                <i class="nav-icon fas fa-users-cog" style="width: 20px; text-align: center;"></i>
                <p style="margin: 0; margin-left: 8px;">平台用户</p>
              </a>
            </li>

            <!-- Reports -->
            <li class="nav-item mb-1">
              <a href="#" class="nav-link" style="padding: 10px 12px; border-radius: 8px; margin-bottom: 4px;">
                <i class="nav-icon fas fa-chart-bar" style="width: 20px; text-align: center;"></i>
                <p style="margin: 0; margin-left: 8px; display: flex; align-items: center; justify-content: space-between;">
                  统计报告
                  <i class="fas fa-angle-left" style="transition: transform 0.3s ease;"></i>
                </p>
              </a>
              <ul class="nav nav-treeview" style="padding-left: 12px; margin-top: 4px;">
                <li class="nav-item">
                  <a href="/reports/platform" class="nav-link" style="padding: 8px 16px; border-radius: 6px; margin-bottom: 2px;">
                    <i class="far fa-circle nav-icon" style="width: 16px; text-align: center; font-size: 8px;"></i>
                    <p style="margin: 0; margin-left: 12px; font-size: 14px;">平台统计</p>
                  </a>
                </li>
                <li class="nav-item">
                  <a href="/reports/revenue" class="nav-link" style="padding: 8px 16px; border-radius: 6px; margin-bottom: 2px;">
                    <i class="far fa-circle nav-icon" style="width: 16px; text-align: center; font-size: 8px;"></i>
                    <p style="margin: 0; margin-left: 12px; font-size: 14px;">收入统计</p>
                  </a>
                </li>
                <li class="nav-item">
                  <a href="/reports/usage" class="nav-link" style="padding: 8px 16px; border-radius: 6px; margin-bottom: 2px;">
                    <i class="far fa-circle nav-icon" style="width: 16px; text-align: center; font-size: 8px;"></i>
                    <p style="margin: 0; margin-left: 12px; font-size: 14px;">使用统计</p>
                  </a>
                </li>
              </ul>
            </li>

            <!-- System Settings -->
            <li class="nav-item mb-1">
              <a href="#" class="nav-link" style="padding: 10px 12px; border-radius: 8px; margin-bottom: 4px;">
                <i class="nav-icon fas fa-cogs" style="width: 20px; text-align: center;"></i>
                <p style="margin: 0; margin-left: 8px; display: flex; align-items: center; justify-content: space-between;">
                  系统管理
                  <i class="fas fa-angle-left" style="transition: transform 0.3s ease;"></i>
                </p>
              </a>
              <ul class="nav nav-treeview" style="padding-left: 12px; margin-top: 4px;">
                <li class="nav-item">
                  <a href="/system/settings" class="nav-link" style="padding: 8px 16px; border-radius: 6px; margin-bottom: 2px;">
                    <i class="far fa-circle nav-icon" style="width: 16px; text-align: center; font-size: 8px;"></i>
                    <p style="margin: 0; margin-left: 12px; font-size: 14px;">系统设置</p>
                  </a>
                </li>
                <li class="nav-item">
                  <a href="/system/logs" class="nav-link" style="padding: 8px 16px; border-radius: 6px; margin-bottom: 2px;">
                    <i class="far fa-circle nav-icon" style="width: 16px; text-align: center; font-size: 8px;"></i>
                    <p style="margin: 0; margin-left: 12px; font-size: 14px;">系统日志</p>
                  </a>
                </li>
                <li class="nav-item">
                  <a href="/system/backups" class="nav-link" style="padding: 8px 16px; border-radius: 6px; margin-bottom: 2px;">
                    <i class="far fa-circle nav-icon" style="width: 16px; text-align: center; font-size: 8px;"></i>
                    <p style="margin: 0; margin-left: 12px; font-size: 14px;">数据备份</p>
                  </a>
                </li>
                <li class="nav-item">
                  <a href="/system/maintenance" class="nav-link" style="padding: 8px 16px; border-radius: 6px; margin-bottom: 2px;">
                    <i class="far fa-circle nav-icon" style="width: 16px; text-align: center; font-size: 8px;"></i>
                    <p style="margin: 0; margin-left: 12px; font-size: 14px;">系统维护</p>
                  </a>
                </li>
              </ul>
            </li>
          </ul>
        </nav>
      </div>
    </aside>

    <!-- Content Wrapper -->
    <div class="content-wrapper">
      <!-- Content Header -->
      <div class="content-header">
        <div class="container-fluid">
          <div class="row mb-2">
            <div class="col-sm-6">
              <h1 class="m-0">
                <%= title %>
              </h1>
            </div>
            <div class="col-sm-6">
              <ol class="breadcrumb float-sm-end">
                <li class="breadcrumb-item"><a href="/dashboard">首页</a></li>
                <li class="breadcrumb-item active">
                  <%= title %>
                </li>
              </ol>
            </div>
          </div>
        </div>
      </div>

      <!-- Main content -->
      <section class="content">
        <div class="container-fluid">
          <%- body %>
        </div>
      </section>
    </div>

    <!-- Footer -->
    <footer class="main-footer">
      <strong>Copyright &copy; 2024 智慧养鹅SAAS管理平台.</strong>
      版本 1.0.0
      <div class="float-end d-none d-sm-inline-block">
        <b>技术支持:</b> Smart Goose Team
      </div>
    </footer>
  </div>

  <!-- Loading overlay -->
  <div id="loadingOverlay" class="loading-overlay" style="display: none;">
    <div class="loading-content">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      <p class="mt-2">处理中...</p>
    </div>
  </div>

  <!-- Toast container -->
  <div class="toast-container position-fixed top-0 end-0 p-3">
    <div id="mainToast" class="toast align-items-center border-0" role="alert" aria-live="assertive" aria-atomic="true">
      <div class="d-flex">
        <div class="toast-body" id="toastMessage">
          <!-- Toast message will be inserted here -->
        </div>
        <button type="button" class="btn-close me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
      </div>
    </div>
  </div>

  <!-- jQuery -->
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

  <!-- Bootstrap 5 JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

  <!-- AdminLTE JS -->
  <script src="https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/js/adminlte.min.js"></script>

  <!-- Axios for HTTP requests -->
  <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>

  <!-- Custom JS -->
  <script src="/js/admin-common.js"></script>

  <script>
    // Event listeners for dynamic elements
    document.addEventListener('DOMContentLoaded', function () {
      // Logout button event listener
      const logoutBtn = document.getElementById('logoutBtn');
      if (logoutBtn) {
        logoutBtn.addEventListener('click', function (e) {
          e.preventDefault();
          logout();
        });
      }

      // Handle sidebar logo error
      const sidebarLogo = document.getElementById('sidebarLogo');
      if (sidebarLogo) {
        sidebarLogo.addEventListener('error', function () {
          this.style.display = 'none';
        });
      }
    });
  </script>
</body>

</html>