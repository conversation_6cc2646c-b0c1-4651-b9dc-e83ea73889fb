<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>布局测试 - SAAS管理平台</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- AdminLTE CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/css/adminlte.min.css">
    
    <style>
        /* 顶栏优化样式 - 横向满屏布局 */
        .main-header.navbar {
            height: 60px;
            padding: 0 2rem;
            position: fixed !important;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1030;
            width: 100% !important;
            margin-left: 0 !important;
        }

        .navbar-nav {
            gap: 0.25rem;
        }

        .navbar-nav .nav-link {
            display: flex;
            align-items: center;
            padding: 0.5rem 0.75rem;
            transition: all 0.3s ease;
            border-radius: 6px;
            white-space: nowrap;
        }

        .navbar-nav .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.15);
            transform: translateY(-1px);
        }

        /* 确保内容区不被导航栏遮挡 - 横向满屏布局调整 */
        .content-wrapper {
            margin-top: 60px;
            margin-left: 250px; /* 为侧边栏留出空间 */
        }

        .main-sidebar {
            padding-top: 60px;
            position: fixed;
            top: 0;
            left: 0;
            width: 250px;
            height: 100vh;
            z-index: 1020;
        }

        /* 响应式布局调整 */
        @media (max-width: 768px) {
            .main-sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            
            .main-sidebar.sidebar-open {
                transform: translateX(0);
            }
            
            .content-wrapper {
                margin-left: 0;
            }
            
            .main-header.navbar {
                padding: 0 1rem;
            }
        }

        @media (min-width: 769px) {
            .main-sidebar {
                transform: translateX(0);
            }
        }
    </style>
</head>

<body class="hold-transition sidebar-mini layout-fixed">
    <div class="wrapper">
        <!-- Navbar -->
        <nav class="main-header navbar navbar-expand navbar-primary navbar-dark"
            style="background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <!-- Left navbar links - 横向满屏布局 -->
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link" data-widget="pushmenu" href="#" role="button" title="切换侧边栏">
                        <i class="fas fa-bars"></i>
                    </a>
                </li>
                <li class="nav-item d-none d-sm-inline-block">
                    <a href="/dashboard" class="nav-link">
                        <i class="fas fa-home me-1"></i>首页
                    </a>
                </li>
                <li class="nav-item d-none d-md-inline-block">
                    <a href="/tenants" class="nav-link">
                        <i class="fas fa-building me-1"></i>租户管理
                    </a>
                </li>
                <li class="nav-item d-none d-lg-inline-block">
                    <a href="/monitoring" class="nav-link">
                        <i class="fas fa-chart-line me-1"></i>监控
                    </a>
                </li>
                <li class="nav-item d-none d-lg-inline-block">
                    <a href="/settings" class="nav-link">
                        <i class="fas fa-cog me-1"></i>设置
                    </a>
                </li>
            </ul>

            <!-- Right navbar links -->
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" href="#" role="button">
                        <i class="fas fa-user me-1"></i>管理员
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" role="button">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Main Sidebar Container -->
        <aside class="main-sidebar sidebar-dark-info elevation-4"
            style="background: linear-gradient(180deg, #1e3c72 0%, #2a5298 50%, #1a365d 100%);">
            <!-- Brand Logo - 移除智慧养鹅字样 -->
            <div class="brand-link" style="background: rgba(255,255,255,0.1); border-bottom: 1px solid rgba(255,255,255,0.1); padding: 0.8rem 1rem;">
                <!-- 移除了logo和文字，保留空间用于其他内容 -->
            </div>

            <!-- Sidebar -->
            <div class="sidebar">
                <!-- Sidebar Menu -->
                <nav class="mt-2">
                    <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false" style="padding: 8px;">
                        <!-- Dashboard -->
                        <li class="nav-item mb-1">
                            <a href="/dashboard" class="nav-link" style="padding: 10px 12px; border-radius: 8px; margin-bottom: 4px;">
                                <i class="nav-icon fas fa-tachometer-alt" style="width: 20px; text-align: center;"></i>
                                <p style="margin: 0; margin-left: 8px;">平台仪表盘</p>
                            </a>
                        </li>

                        <!-- 租户管理 -->
                        <li class="nav-item mb-1">
                            <a href="/tenants" class="nav-link" style="padding: 10px 12px; border-radius: 8px; margin-bottom: 4px;">
                                <i class="nav-icon fas fa-building" style="width: 20px; text-align: center;"></i>
                                <p style="margin: 0; margin-left: 8px;">租户管理</p>
                            </a>
                        </li>

                        <!-- API管理 -->
                        <li class="nav-item mb-1">
                            <a href="/api-docs" class="nav-link" style="padding: 10px 12px; border-radius: 8px; margin-bottom: 4px;">
                                <i class="nav-icon fas fa-code" style="width: 20px; text-align: center;"></i>
                                <p style="margin: 0; margin-left: 8px;">API管理</p>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </aside>

        <!-- Content Wrapper -->
        <div class="content-wrapper">
            <!-- Content Header -->
            <div class="content-header">
                <div class="container-fluid">
                    <div class="row mb-2">
                        <div class="col-sm-6">
                            <h1 class="m-0">测试页面</h1>
                        </div>
                        <div class="col-sm-6">
                            <ol class="breadcrumb float-sm-end">
                                <li class="breadcrumb-item"><a href="/dashboard">首页</a></li>
                                <li class="breadcrumb-item active">测试页面</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main content -->
            <section class="content">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">布局测试</h3>
                                </div>
                                <div class="card-body">
                                    <p>这是一个测试页面，用于验证以下修改：</p>
                                    <ul>
                                        <li>✅ 移除了侧边栏中的"智慧养鹅"字样</li>
                                        <li>✅ 顶栏修改为横向满屏布局</li>
                                        <li>✅ 响应式设计适配移动端</li>
                                        <li>✅ 保持了原有的功能和导航结构</li>
                                    </ul>
                                    
                                    <div class="alert alert-success mt-3">
                                        <h5><i class="icon fas fa-check"></i> 修改完成！</h5>
                                        所有"智慧养鹅"字样已成功移除，顶栏已修改为横向满屏布局。
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>

        <!-- Footer -->
        <footer class="main-footer">
            <strong>Copyright &copy; 2024 SAAS管理平台.</strong>
            版本 1.0.0
            <div class="float-end d-none d-sm-inline-block">
                <b>技术支持:</b> Smart Goose Team
            </div>
        </footer>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- AdminLTE JS -->
    <script src="https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/js/adminlte.min.js"></script>
</body>
</html>
