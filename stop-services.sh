#!/bin/bash

# 停止所有服务脚本

echo "🛑 停止所有服务..."

# 1. 从PID文件读取进程ID并停止
if [ -f ".service_pids" ]; then
    echo "📖 从PID文件读取进程信息..."
    source .service_pids
    
    if [ "$SERVER_PID" != "未启动" ] && [ -n "$SERVER_PID" ]; then
        echo "🛑 停止后端服务 (PID: $SERVER_PID)..."
        kill $SERVER_PID 2>/dev/null || true
    fi
    
    if [ "$SAAS_PID" != "未启动" ] && [ -n "$SAAS_PID" ]; then
        echo "🛑 停止SaaS管理后台 (PID: $SAAS_PID)..."
        kill $SAAS_PID 2>/dev/null || true
    fi
    
    if [ "$ADMIN_PID" != "未启动" ] && [ -n "$ADMIN_PID" ]; then
        echo "🛑 停止管理后台 (PID: $ADMIN_PID)..."
        kill $ADMIN_PID 2>/dev/null || true
    fi
fi

# 2. 强制停止所有相关进程
echo "🔍 查找并停止所有相关进程..."
pkill -f "node.*server.js" 2>/dev/null || true
pkill -f "node.*app.js" 2>/dev/null || true
pkill -f "npm.*start" 2>/dev/null || true

# 3. 检查端口占用并强制释放
echo "🔍 检查端口占用..."
for port in 3000 3001 3002; do
    pid=$(lsof -ti :$port 2>/dev/null)
    if [ -n "$pid" ]; then
        echo "🛑 强制停止占用端口 $port 的进程 (PID: $pid)..."
        kill -9 $pid 2>/dev/null || true
    fi
done

# 4. 等待进程完全停止
echo "⏳ 等待进程完全停止..."
sleep 3

# 5. 验证停止状态
echo "🔍 验证停止状态..."
for port in 3000 3001 3002; do
    if lsof -i :$port >/dev/null 2>&1; then
        echo "⚠️ 端口 $port 仍被占用"
    else
        echo "✅ 端口 $port 已释放"
    fi
done

# 6. 清理PID文件
if [ -f ".service_pids" ]; then
    rm .service_pids
    echo "🧹 已清理PID文件"
fi

echo "✅ 所有服务已停止！"
