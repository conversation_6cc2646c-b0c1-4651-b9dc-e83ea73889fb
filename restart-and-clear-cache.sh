#!/bin/bash

# 重启服务并清除缓存脚本
# 用于解决修改后不生效的问题

echo "🔄 开始重启服务并清除缓存..."

# 1. 停止所有相关服务
echo "📴 停止现有服务..."
pkill -f "node.*server.js" 2>/dev/null || true
pkill -f "node.*app.js" 2>/dev/null || true
pkill -f "npm.*start" 2>/dev/null || true

# 等待进程完全停止
sleep 2

# 2. 清除Node.js缓存
echo "🧹 清除Node.js缓存..."
if [ -d "node_modules/.cache" ]; then
    rm -rf node_modules/.cache
    echo "✅ 清除了 node_modules/.cache"
fi

# 3. 清除临时文件
echo "🧹 清除临时文件..."
find . -name "*.tmp" -delete 2>/dev/null || true
find . -name ".DS_Store" -delete 2>/dev/null || true

# 4. 清除日志文件（可选）
echo "🧹 清除旧日志文件..."
if [ -d "logs" ]; then
    find logs -name "*.log" -mtime +1 -delete 2>/dev/null || true
    echo "✅ 清除了旧日志文件"
fi

# 5. 重新安装依赖（如果需要）
if [ "$1" = "--reinstall" ]; then
    echo "📦 重新安装依赖..."
    rm -rf node_modules package-lock.json
    npm install
fi

# 6. 启动后端服务
echo "🚀 启动后端API服务..."
cd backend
if [ -f "server.js" ]; then
    nohup node server.js > ../logs/server.log 2>&1 &
    SERVER_PID=$!
    echo "✅ 后端服务已启动 (PID: $SERVER_PID)"
else
    echo "❌ 未找到 backend/server.js"
fi

# 7. 启动SaaS管理后台
echo "🚀 启动SaaS管理后台..."
cd ../backend/saas-admin
if [ -f "server.js" ]; then
    nohup node server.js > ../../logs/saas-admin.log 2>&1 &
    SAAS_PID=$!
    echo "✅ SaaS管理后台已启动 (PID: $SAAS_PID)"
else
    echo "❌ 未找到 backend/saas-admin/server.js"
fi

# 8. 启动普通管理后台
echo "🚀 启动管理后台..."
cd ../admin
if [ -f "start-admin.js" ]; then
    nohup node start-admin.js > ../../logs/admin.log 2>&1 &
    ADMIN_PID=$!
    echo "✅ 管理后台已启动 (PID: $ADMIN_PID)"
else
    echo "❌ 未找到 backend/admin/start-admin.js"
fi

cd ../..

# 9. 等待服务启动
echo "⏳ 等待服务启动..."
sleep 5

# 10. 检查服务状态
echo "🔍 检查服务状态..."

# 检查端口占用
check_port() {
    local port=$1
    local service_name=$2
    if lsof -i :$port >/dev/null 2>&1; then
        echo "✅ $service_name 正在运行 (端口 $port)"
        return 0
    else
        echo "❌ $service_name 未运行 (端口 $port)"
        return 1
    fi
}

# 检查各个服务
check_port 3000 "后端API服务"
check_port 3001 "SaaS管理后台"
check_port 3002 "管理后台"

# 11. 测试HTTP连接
echo "🌐 测试HTTP连接..."
test_url() {
    local url=$1
    local service_name=$2
    if curl -s --max-time 5 "$url" >/dev/null 2>&1; then
        echo "✅ $service_name 连接正常 ($url)"
    else
        echo "⚠️ $service_name 连接失败 ($url)"
    fi
}

test_url "http://localhost:3000/api/health" "后端API"
test_url "http://localhost:3001/dashboard" "SaaS管理后台"
test_url "http://localhost:3002/dashboard" "管理后台"

# 12. 显示浏览器缓存清除提示
echo ""
echo "🌐 浏览器缓存清除提示："
echo "1. 按 Ctrl+Shift+R (Windows/Linux) 或 Cmd+Shift+R (Mac) 强制刷新"
echo "2. 或者按 F12 打开开发者工具，右键刷新按钮选择'清空缓存并硬性重新加载'"
echo "3. 或者在浏览器设置中清除缓存和Cookie"

# 13. 显示访问地址
echo ""
echo "🔗 服务访问地址："
echo "• 后端API: http://localhost:3000"
echo "• SaaS管理后台: http://localhost:3001"
echo "• 管理后台: http://localhost:3002"

echo ""
echo "✅ 重启和缓存清除完成！"
echo "💡 如果修改仍未生效，请："
echo "   1. 检查是否还有其他CSS文件覆盖了修改"
echo "   2. 确认修改的文件是否被正确加载"
echo "   3. 查看浏览器开发者工具的Network标签，确认资源是否重新加载"

# 14. 保存进程ID到文件
echo "📝 保存进程信息..."
cat > .service_pids << EOF
# 服务进程ID文件
# 生成时间: $(date)
SERVER_PID=${SERVER_PID:-"未启动"}
SAAS_PID=${SAAS_PID:-"未启动"}
ADMIN_PID=${ADMIN_PID:-"未启动"}
EOF

echo "进程信息已保存到 .service_pids 文件"
